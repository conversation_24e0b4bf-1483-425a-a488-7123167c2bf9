#!/usr/bin/env python3
"""
启动问题修复验证脚本
验证所有启动问题是否已解决
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_redis_import():
    """测试Redis导入"""
    try:
        import redis.asyncio as redis
        logger.info("✅ Redis模块导入成功")
        return True
    except ImportError as e:
        logger.error(f"❌ Redis模块导入失败: {str(e)}")
        return False


async def test_redis_cache_service():
    """测试Redis缓存服务"""
    try:
        from app.services.redis_cache_service import redis_cache
        # 不实际连接Redis，只测试初始化
        logger.info("✅ Redis缓存服务导入成功")
        return True
    except Exception as e:
        logger.error(f"❌ Redis缓存服务测试失败: {str(e)}")
        return False


async def test_neo4j_queries():
    """测试Neo4j查询修复"""
    try:
        from app.services.text2sql_utils import retrieve_relevant_schema
        logger.info("✅ Neo4j查询代码导入成功（已添加错误处理）")
        return True
    except Exception as e:
        logger.error(f"❌ Neo4j查询测试失败: {str(e)}")
        return False


async def test_service_configurations():
    """测试服务配置"""
    try:
        from app.core.config import settings

        logger.info("=== 服务配置状态 ===")
        logger.info(f"Redis启用状态: {getattr(settings, 'REDIS_ENABLED', False)}")
        logger.info(f"Milvus启用状态: {getattr(settings, 'MILVUS_ENABLED', False)}")
        logger.info(f"混合检索启用状态: {settings.HYBRID_RETRIEVAL_ENABLED}")

        # 检查配置一致性
        if settings.HYBRID_RETRIEVAL_ENABLED and not getattr(settings, 'MILVUS_ENABLED', False):
            logger.warning("⚠️ 混合检索已启用但Milvus未启用，可能导致初始化失败")
            return False

        logger.info("✅ 服务配置检查通过")
        return True
    except Exception as e:
        logger.error(f"❌ 服务配置测试失败: {str(e)}")
        return False


async def test_fastapi_lifespan():
    """测试FastAPI生命周期修复"""
    try:
        # 检查main.py是否使用了新的lifespan模式
        main_file = Path(__file__).parent / "main.py"
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "@app.on_event" in content:
            logger.warning("⚠️ main.py仍在使用已弃用的@app.on_event装饰器")
            return False
        elif "lifespan" in content and "@asynccontextmanager" in content:
            logger.info("✅ FastAPI已更新为使用新的lifespan事件处理器")
            return True
        else:
            logger.warning("⚠️ 无法确定FastAPI事件处理器状态")
            return False
    except Exception as e:
        logger.error(f"❌ FastAPI生命周期测试失败: {str(e)}")
        return False


async def test_embedding_cache_directory():
    """测试嵌入模型缓存目录修复"""
    try:
        import os
        import tempfile
        
        # 测试缓存目录逻辑
        cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
        if not os.path.exists(cache_dir):
            cache_dir = os.path.join(tempfile.gettempdir(), "huggingface_cache")
        
        logger.info(f"嵌入模型缓存目录: {cache_dir}")
        
        # 检查目录是否可写
        test_file = os.path.join(cache_dir, "test_write.tmp")
        try:
            os.makedirs(cache_dir, exist_ok=True)
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            logger.info("✅ 缓存目录可写")
            return True
        except Exception as e:
            logger.error(f"❌ 缓存目录不可写: {str(e)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 嵌入模型缓存目录测试失败: {str(e)}")
        return False


async def test_startup_sequence():
    """测试启动序列"""
    try:
        from app.core.startup import startup_sequence
        logger.info("✅ 启动序列代码导入成功")
        return True
    except Exception as e:
        logger.error(f"❌ 启动序列测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    logger.info("🔍 开始验证启动问题修复...")
    
    tests = [
        ("Redis模块导入", test_redis_import()),
        ("Redis缓存服务", test_redis_cache_service()),
        ("Neo4j查询修复", test_neo4j_queries()),
        ("服务配置检查", test_service_configurations()),
        ("FastAPI生命周期", test_fastapi_lifespan()),
        ("嵌入模型缓存目录", test_embedding_cache_directory()),
        ("启动序列", test_startup_sequence()),
    ]
    
    results = []
    for test_name, test_coro in tests:
        logger.info(f"\n--- 测试: {test_name} ---")
        try:
            result = await test_coro
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {str(e)}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n=== 修复验证结果 ===")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{len(results)} 项修复验证通过")
    
    if passed == len(results):
        logger.info("🎉 所有启动问题已修复！")
        logger.info("\n📋 修复总结:")
        logger.info("1. ✅ 添加了Redis依赖包")
        logger.info("2. ✅ 修复了FastAPI弃用警告（使用lifespan）")
        logger.info("3. ✅ 添加了Neo4j REFERENCES关系的错误处理")
        logger.info("4. ✅ 修复了嵌入模型缓存目录权限问题")
        logger.info("5. ✅ 添加了混合检索引擎的可选配置")
    else:
        logger.error("⚠️ 部分修复验证失败，请检查具体问题")
        
        logger.info("\n🛠️ 故障排除建议:")
        logger.info("1. 确保已安装所有依赖: pip install -r requirements.txt")
        logger.info("2. 检查.env配置文件是否正确")
        logger.info("3. 如果不需要混合检索，设置 HYBRID_RETRIEVAL_ENABLED=false")
        logger.info("4. 确保有足够的磁盘空间和网络连接")


if __name__ == "__main__":
    asyncio.run(main())
