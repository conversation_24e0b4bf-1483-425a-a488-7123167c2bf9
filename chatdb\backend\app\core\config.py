import os
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    API_V1_STR: str = "/v1"
    SECRET_KEY: str = os.getenv("SECRET_KEY", "development_secret_key")

    # CORS settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Database settings
    DATABASE_TYPE: str = os.getenv("DATABASE_TYPE", "sqlite")

    # SQLite settings
    SQLITE_DB_PATH: str = os.getenv("SQLITE_DB_PATH", "resource.db")

    # 多数据库配置
    METADATA_DB_PATH: str = os.getenv("METADATA_DB_PATH", "resource.db")
    BUSINESS_DB_PATH: str = os.getenv("BUSINESS_DB_PATH", "fin_data.db")

    # 元数据增强配置
    ENABLE_METADATA_ENHANCEMENT: bool = os.getenv("ENABLE_METADATA_ENHANCEMENT", "true").lower() == "true"

    # 增强提示系统配置
    ENABLE_ENHANCED_PROMPTS: bool = os.getenv("ENABLE_ENHANCED_PROMPTS", "true").lower() == "true"
    ENHANCED_PROMPT_VERSION: str = os.getenv("ENHANCED_PROMPT_VERSION", "v2.0")

    # 数据质量检查配置
    ENABLE_DATA_QUALITY_CHECK: bool = os.getenv("ENABLE_DATA_QUALITY_CHECK", "true").lower() == "true"
    DATA_QUALITY_SEVERITY_LEVEL: str = os.getenv("DATA_QUALITY_SEVERITY_LEVEL", "WARNING")

    # AI提示优化配置
    AI_PROMPT_TEMPLATE_VERSION: str = os.getenv("AI_PROMPT_TEMPLATE_VERSION", "enhanced")
    ENABLE_QUERY_PATTERN_MATCHING: bool = os.getenv("ENABLE_QUERY_PATTERN_MATCHING", "true").lower() == "true"

    # 调试配置
    DEBUG_ENHANCED_PROMPTS: bool = os.getenv("DEBUG_ENHANCED_PROMPTS", "false").lower() == "true"
    LOG_PROMPT_DETAILS: bool = os.getenv("LOG_PROMPT_DETAILS", "false").lower() == "true"

    # 性能优化配置
    ENABLE_QUERY_CACHE: bool = os.getenv("ENABLE_QUERY_CACHE", "true").lower() == "true"
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "3600"))  # 缓存过期时间（秒）
    MAX_CACHE_SIZE: int = int(os.getenv("MAX_CACHE_SIZE", "1000"))  # 最大缓存项数

    # 数据库连接池配置
    DB_POOL_SIZE: int = int(os.getenv("DB_POOL_SIZE", "10"))
    DB_MAX_OVERFLOW: int = int(os.getenv("DB_MAX_OVERFLOW", "20"))
    DB_POOL_TIMEOUT: int = int(os.getenv("DB_POOL_TIMEOUT", "30"))
    DB_POOL_RECYCLE: int = int(os.getenv("DB_POOL_RECYCLE", "3600"))

    # 查询优化配置
    DEFAULT_QUERY_LIMIT: int = int(os.getenv("DEFAULT_QUERY_LIMIT", "1000"))
    MAX_QUERY_TIMEOUT: int = int(os.getenv("MAX_QUERY_TIMEOUT", "30"))  # 查询超时时间（秒）
    ENABLE_QUERY_OPTIMIZATION: bool = os.getenv("ENABLE_QUERY_OPTIMIZATION", "true").lower() == "true"

    # 监控配置
    ENABLE_PERFORMANCE_MONITORING: bool = os.getenv("ENABLE_PERFORMANCE_MONITORING", "true").lower() == "true"
    SLOW_QUERY_THRESHOLD: float = float(os.getenv("SLOW_QUERY_THRESHOLD", "5.0"))  # 慢查询阈值（秒）
    MEMORY_WARNING_THRESHOLD: float = float(os.getenv("MEMORY_WARNING_THRESHOLD", "80.0"))  # 内存警告阈值（%）

    # MySQL settings (保留以备后用)
    MYSQL_SERVER: str = os.getenv("MYSQL_SERVER", "localhost")
    MYSQL_USER: str = os.getenv("MYSQL_USER", "root")
    MYSQL_PASSWORD: str = os.getenv("MYSQL_PASSWORD", "mysql")
    MYSQL_DB: str = os.getenv("MYSQL_DB", "chatdb")
    MYSQL_PORT: str = os.getenv("MYSQL_PORT", "3306")

    # Neo4j settings
    NEO4J_URI: str = os.getenv("NEO4J_URI", "bolt://**************:7687")
    NEO4J_USER: str = os.getenv("NEO4J_USER", "neo4j")
    NEO4J_PASSWORD: str = os.getenv("NEO4J_PASSWORD", "65132090")

    # LLM settings - 阿里云百炼API
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "sk-04d0650a86124b31be03bb6c7bbaa036")
    OPENAI_API_BASE: Optional[str] = os.getenv("OPENAI_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
    LLM_MODEL: str = os.getenv("LLM_MODEL", "qwen-max")
    LLM_TEMPERATURE: float = float(os.getenv("LLM_TEMPERATURE", "0.1"))
    LLM_MAX_TOKENS: int = int(os.getenv("LLM_MAX_TOKENS", "4000"))

    # Redis配置
    REDIS_ENABLED: bool = os.getenv("REDIS_ENABLED", "false").lower() == "true"
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_PASSWORD: Optional[str] = os.getenv("REDIS_PASSWORD", None)

    # Milvus配置
    MILVUS_ENABLED: bool = os.getenv("MILVUS_ENABLED", "true").lower() == "true"  # 您已启动Milvus
    MILVUS_HOST: str = os.getenv("MILVUS_HOST", "localhost")
    MILVUS_PORT: str = os.getenv("MILVUS_PORT", "19530")

    # 向量模型配置
    EMBEDDING_MODEL: str = os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2")
    VECTOR_DIMENSION: int = int(os.getenv("VECTOR_DIMENSION", "384"))

    # 混合检索配置
    HYBRID_RETRIEVAL_ENABLED: bool = os.getenv("HYBRID_RETRIEVAL_ENABLED", "true").lower() == "true"
    SEMANTIC_WEIGHT: float = float(os.getenv("SEMANTIC_WEIGHT", "0.35"))
    STRUCTURAL_WEIGHT: float = float(os.getenv("STRUCTURAL_WEIGHT", "0.35"))
    PATTERN_WEIGHT: float = float(os.getenv("PATTERN_WEIGHT", "0.20"))
    QUALITY_WEIGHT: float = float(os.getenv("QUALITY_WEIGHT", "0.10"))

    # 学习配置
    AUTO_LEARNING_ENABLED: bool = os.getenv("AUTO_LEARNING_ENABLED", "true").lower() == "true"
    FEEDBACK_LEARNING_ENABLED: bool = os.getenv("FEEDBACK_LEARNING_ENABLED", "true").lower() == "true"
    PATTERN_DISCOVERY_ENABLED: bool = os.getenv("PATTERN_DISCOVERY_ENABLED", "true").lower() == "true"

    # 性能配置
    RETRIEVAL_CACHE_TTL: int = int(os.getenv("RETRIEVAL_CACHE_TTL", "3600"))
    MAX_EXAMPLES_PER_QUERY: int = int(os.getenv("MAX_EXAMPLES_PER_QUERY", "5"))
    PARALLEL_RETRIEVAL: bool = os.getenv("PARALLEL_RETRIEVAL", "true").lower() == "true"

    class Config:
        case_sensitive = True
        env_file = ".env"


settings = Settings()
