{"timestamp": "2025-07-29T23:44:28.649512", "changes": [{"type": "ADD_COLUMN", "database": "resource.db", "table": "column_descriptions", "column": "field_category", "sql": "ALTER TABLE column_descriptions ADD COLUMN field_category TEXT DEFAULT ''", "timestamp": "2025-07-29T23:44:28.668514"}, {"type": "ADD_COLUMN", "database": "resource.db", "table": "column_descriptions", "column": "usage_scenarios", "sql": "ALTER TABLE column_descriptions ADD COLUMN usage_scenarios TEXT DEFAULT ''", "timestamp": "2025-07-29T23:44:28.671513"}, {"type": "ADD_COLUMN", "database": "resource.db", "table": "column_descriptions", "column": "common_values", "sql": "ALTER TABLE column_descriptions ADD COLUMN common_values TEXT DEFAULT ''", "timestamp": "2025-07-29T23:44:28.676519"}, {"type": "ADD_COLUMN", "database": "resource.db", "table": "column_descriptions", "column": "related_fields", "sql": "ALTER TABLE column_descriptions ADD COLUMN related_fields TEXT DEFAULT ''", "timestamp": "2025-07-29T23:44:28.680514"}, {"type": "ADD_COLUMN", "database": "resource.db", "table": "column_descriptions", "column": "calculation_rules", "sql": "ALTER TABLE column_descriptions ADD COLUMN calculation_rules TEXT DEFAULT ''", "timestamp": "2025-07-29T23:44:28.683515"}, {"type": "ADD_COLUMN", "database": "resource.db", "table": "column_descriptions", "column": "ai_prompt_hints", "sql": "ALTER TABLE column_descriptions ADD COLUMN ai_prompt_hints TEXT DEFAULT ''", "timestamp": "2025-07-29T23:44:28.687513"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 6, "table_name": "financial_data", "rule_category": "科目编号识别规则", "rule_description": "资产类科目编号以1开头(1001-1999)，负债类以2开头(2001-2999)，所有者权益类以3开头(3001-3999)，成本类以4开头(4001-4999)，损益类以5-6开头(5001-6999)", "sql_example": "SELECT account_code, account_name FROM financial_data WHERE account_code BETWEEN 1001 AND 1999 LIMIT 5", "importance_level": "CRITICAL", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.728054"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 7, "table_name": "financial_data", "rule_category": "收入科目细分规则", "rule_description": "主营业务收入(6001-6099)，其他业务收入(6101-6199)，投资收益(6301-6399)，营业外收入(6701-6799)，所有收入类科目必须使用credit_amount字段", "sql_example": "SELECT account_code, account_name, SUM(credit_amount) as 收入金额 FROM financial_data WHERE account_code BETWEEN 6001 AND 6799 GROUP BY account_code, account_name", "importance_level": "CRITICAL", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.734058"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 8, "table_name": "financial_data", "rule_category": "费用科目细分规则", "rule_description": "主营业务成本(6401-6499)，销售费用(6601-6699)，管理费用(6602-6699)，财务费用(6603-6699)，所有费用类科目必须使用debit_amount字段", "sql_example": "SELECT account_code, account_name, SUM(debit_amount) as 费用金额 FROM financial_data WHERE account_code BETWEEN 6401 AND 6699 GROUP BY account_code, account_name", "importance_level": "CRITICAL", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.735055"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 9, "table_name": "financial_data", "rule_category": "资产科目规则", "rule_description": "流动资产(1001-1199)，非流动资产(1201-1999)，所有资产类科目必须使用balance字段，且需要CAST转换", "sql_example": "SELECT account_code, account_name, SUM(CAST(balance AS REAL)) as 资产余额 FROM financial_data WHERE account_code BETWEEN 1001 AND 1999 GROUP BY account_code, account_name", "importance_level": "CRITICAL", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.736061"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 10, "table_name": "financial_data", "rule_category": "负债科目规则", "rule_description": "流动负债(2001-2199)，非流动负债(2201-2999)，所有负债类科目必须使用balance字段，且需要CAST转换", "sql_example": "SELECT account_code, account_name, SUM(CAST(balance AS REAL)) as 负债余额 FROM financial_data WHERE account_code BETWEEN 2001 AND 2999 GROUP BY account_code, account_name", "importance_level": "CRITICAL", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.736061"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 11, "table_name": "financial_data", "rule_category": "时间筛选规则", "rule_description": "查询特定时间段时，必须同时使用year和month字段进行筛选，月份范围1-12", "sql_example": "SELECT year, month, COUNT(*) as 记录数 FROM financial_data WHERE year = 2024 AND month = 9 GROUP BY year, month", "importance_level": "HIGH", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.736061"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 12, "table_name": "financial_data", "rule_category": "组织筛选规则", "rule_description": "按组织查询时，可使用accounting_organization(数字编号)或accounting_unit_name(名称)进行筛选", "sql_example": "SELECT accounting_organization, accounting_unit_name, COUNT(*) as 记录数 FROM financial_data GROUP BY accounting_organization, accounting_unit_name", "importance_level": "HIGH", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.737057"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 13, "table_name": "financial_data", "rule_category": "金额汇总规则", "rule_description": "进行金额汇总时，必须根据科目类型选择正确的金额字段：资产负债用balance，收入用credit_amount，费用用debit_amount", "sql_example": "SELECT CASE WHEN account_code < 2000 THEN \"资产\" WHEN account_code < 3000 THEN \"负债\" WHEN account_code >= 6000 THEN \"损益\" END as 科目类型, COUNT(*) FROM financial_data GROUP BY 1", "importance_level": "CRITICAL", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.737057"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 14, "table_name": "financial_data", "rule_category": "数据类型处理规则", "rule_description": "balance字段为TEXT类型，在进行数值计算时必须使用CAST(balance AS REAL)进行类型转换", "sql_example": "SELECT CAST(balance AS REAL) as 数值余额 FROM financial_data WHERE balance IS NOT NULL AND balance != \"\" LIMIT 5", "importance_level": "HIGH", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.737057"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 15, "table_name": "financial_data", "rule_category": "空值处理规则", "rule_description": "在进行汇总计算时，应考虑NULL值和空字符串的处理，使用COALESCE或WHERE条件过滤", "sql_example": "SELECT COUNT(*) as 总记录, COUNT(balance) as 非空余额记录 FROM financial_data", "importance_level": "MEDIUM", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.737057"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 16, "table_name": "financial_data", "rule_category": "财务报表编制规则", "rule_description": "编制资产负债表时使用balance字段，编制利润表时收入用credit_amount，费用用debit_amount", "sql_example": "SELECT \"资产负债表示例\" as 报表类型, SUM(CAST(balance AS REAL)) as 金额 FROM financial_data WHERE account_code < 4000", "importance_level": "HIGH", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.738058"}, {"type": "SYNC_DATA", "database": "resource.db", "table": "business_rules", "action": "INSERT", "data": {"id": 17, "table_name": "financial_data", "rule_category": "借贷平衡验证规则", "rule_description": "在会计期间内，借方发生额总和应等于贷方发生额总和，可用于数据完整性验证", "sql_example": "SELECT SUM(debit_amount) as 借方总额, SUM(credit_amount) as 贷方总额, SUM(debit_amount) - SUM(credit_amount) as 差额 FROM financial_data", "importance_level": "MEDIUM", "created_at": "2025-07-28 06:02:04"}, "timestamp": "2025-07-29T23:44:28.738058"}], "errors": [], "verification": {"column_descriptions": {"success": true, "message": "结构一致，共13个字段"}, "business_rules": {"success": true, "message": "resource: 17条, business: 17条", "resource_count": 17, "business_count": 17}}, "summary": {"total_changes": 18, "total_errors": 0, "success": true, "completed_at": "2025-07-29T23:44:28.830474"}}