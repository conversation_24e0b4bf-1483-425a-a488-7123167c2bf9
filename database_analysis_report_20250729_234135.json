{"timestamp": "2025-07-29T23:41:35.662659", "resource_db": {"table_count": 11, "tables": {"dbconnection": {"row_count": 1, "column_count": 10, "columns": [{"name": "id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": true}, {"name": "name", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "db_type", "type": "VARCHAR(50)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "host", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "port", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "username", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "password_encrypted", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "database_name", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "DATETIME", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "DATETIME", "not_null": false, "default_value": null, "primary_key": false}], "indexes": [{"name": "ix_dbconnection_name", "unique": true}, {"name": "ix_dbconnection_id", "unique": false}], "foreign_keys": [], "primary_keys": ["id"]}, "schematable": {"row_count": 10, "column_count": 7, "columns": [{"name": "id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": true}, {"name": "connection_id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "table_name", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "ui_metadata", "type": "JSON", "not_null": false, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "DATETIME", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "DATETIME", "not_null": false, "default_value": null, "primary_key": false}], "indexes": [{"name": "ix_schematable_id", "unique": false}], "foreign_keys": [{"column": "connection_id", "referenced_table": "dbconnection", "referenced_column": "id"}], "primary_keys": ["id"]}, "chatsession": {"row_count": 13, "column_count": 6, "columns": [{"name": "id", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": true}, {"name": "title", "type": "VARCHAR(500)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "connection_id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "DATETIME", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "DATETIME", "not_null": false, "default_value": null, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "default_value": null, "primary_key": false}], "indexes": [{"name": "ix_chatsession_id", "unique": false}, {"name": "sqlite_autoindex_chatsession_1", "unique": true}], "foreign_keys": [{"column": "connection_id", "referenced_table": "dbconnection", "referenced_column": "id"}], "primary_keys": ["id"]}, "schemacolumn": {"row_count": 101, "column_count": 10, "columns": [{"name": "id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": true}, {"name": "table_id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "column_name", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "data_type", "type": "VARCHAR(100)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "is_primary_key", "type": "BOOLEAN", "not_null": false, "default_value": null, "primary_key": false}, {"name": "is_foreign_key", "type": "BOOLEAN", "not_null": false, "default_value": null, "primary_key": false}, {"name": "is_unique", "type": "BOOLEAN", "not_null": false, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "DATETIME", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "DATETIME", "not_null": false, "default_value": null, "primary_key": false}], "indexes": [{"name": "ix_schemacolumn_id", "unique": false}], "foreign_keys": [{"column": "table_id", "referenced_table": "schematable", "referenced_column": "id"}], "primary_keys": ["id"]}, "chatmessage": {"row_count": 0, "column_count": 8, "columns": [{"name": "id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": true}, {"name": "session_id", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "message_type", "type": "VARCHAR(50)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "content", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "message_metadata", "type": "JSON", "not_null": false, "default_value": null, "primary_key": false}, {"name": "region", "type": "VARCHAR(50)", "not_null": false, "default_value": null, "primary_key": false}, {"name": "order_index", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "DATETIME", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "ix_chatmessage_id", "unique": false}], "foreign_keys": [{"column": "session_id", "referenced_table": "chatsession", "referenced_column": "id"}], "primary_keys": ["id"]}, "chathistorysnapshot": {"row_count": 40, "column_count": 5, "columns": [{"name": "id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": true}, {"name": "session_id", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "query", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "response_data", "type": "JSON", "not_null": true, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "DATETIME", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "ix_chathistorysnapshot_id", "unique": false}], "foreign_keys": [{"column": "session_id", "referenced_table": "chatsession", "referenced_column": "id"}], "primary_keys": ["id"]}, "schemarelationship": {"row_count": 0, "column_count": 10, "columns": [{"name": "id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": true}, {"name": "connection_id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "source_table_id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "source_column_id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "target_table_id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "target_column_id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "relationship_type", "type": "VARCHAR(50)", "not_null": false, "default_value": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "DATETIME", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "DATETIME", "not_null": false, "default_value": null, "primary_key": false}], "indexes": [{"name": "ix_schemarelationship_id", "unique": false}], "foreign_keys": [{"column": "target_column_id", "referenced_table": "schemacolumn", "referenced_column": "id"}, {"column": "target_table_id", "referenced_table": "schematable", "referenced_column": "id"}, {"column": "source_column_id", "referenced_table": "schemacolumn", "referenced_column": "id"}, {"column": "source_table_id", "referenced_table": "schematable", "referenced_column": "id"}, {"column": "connection_id", "referenced_table": "dbconnection", "referenced_column": "id"}], "primary_keys": ["id"]}, "valuemapping": {"row_count": 1, "column_count": 6, "columns": [{"name": "id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": true}, {"name": "column_id", "type": "INTEGER", "not_null": true, "default_value": null, "primary_key": false}, {"name": "nl_term", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "db_value", "type": "VARCHAR(255)", "not_null": true, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "DATETIME", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "DATETIME", "not_null": false, "default_value": null, "primary_key": false}], "indexes": [{"name": "ix_valuemapping_nl_term", "unique": false}, {"name": "ix_valuemapping_id", "unique": false}], "foreign_keys": [{"column": "column_id", "referenced_table": "schemacolumn", "referenced_column": "id"}], "primary_keys": ["id"]}, "table_descriptions": {"row_count": 1, "column_count": 5, "columns": [{"name": "table_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": true}, {"name": "description", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "business_purpose", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "data_scale", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_table_descriptions_1", "unique": true}], "foreign_keys": [], "primary_keys": ["table_name"]}, "column_descriptions": {"row_count": 31, "column_count": 7, "columns": [{"name": "table_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": true}, {"name": "column_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": true}, {"name": "chinese_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "data_type", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "business_rules", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "ai_understanding_points", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_column_descriptions_1", "unique": true}], "foreign_keys": [], "primary_keys": ["table_name", "column_name"]}, "business_rules": {"row_count": 5, "column_count": 7, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "table_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "rule_category", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "rule_description", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "sql_example", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "importance_level", "type": "TEXT", "not_null": false, "default_value": "'HIGH'", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [], "foreign_keys": [], "primary_keys": ["id"]}}, "file_size": 2527232}, "business_db": {"table_count": 8, "tables": {"financial_data": {"row_count": 723333, "column_count": 31, "columns": [{"name": "year", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "month", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "accounting_organization", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "accounting_unit_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "account_code", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "account_full_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "account_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "opening_debit_amount", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "opening_credit_amount", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "account_direction", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "project_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "project_code", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "project_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "market_nature_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "tax_rate_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "tax_rate_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "business_format_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "financial_product_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "long_term_deferred_project_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "property_unit_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "cash_flow_project_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "municipal_enterprise_unit_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "bank_account_id", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "financial_institution_id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "bank_routing_number", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "bank_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "debit_amount", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "debit_cumulative", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "credit_amount", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "credit_cumulative", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "balance", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "indexes": [], "foreign_keys": [], "primary_keys": []}, "table_descriptions": {"row_count": 1, "column_count": 5, "columns": [{"name": "table_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": true}, {"name": "description", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "business_purpose", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "data_scale", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_table_descriptions_1", "unique": true}], "foreign_keys": [], "primary_keys": ["table_name"]}, "column_descriptions": {"row_count": 31, "column_count": 13, "columns": [{"name": "table_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": true}, {"name": "column_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": true}, {"name": "chinese_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "data_type", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "business_rules", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "ai_understanding_points", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "field_category", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}, {"name": "usage_scenarios", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}, {"name": "common_values", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}, {"name": "related_fields", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}, {"name": "calculation_rules", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}, {"name": "ai_prompt_hints", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_column_descriptions_1", "unique": true}], "foreign_keys": [], "primary_keys": ["table_name", "column_name"]}, "business_rules": {"row_count": 17, "column_count": 7, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "table_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "rule_category", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "rule_description", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "sql_example", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "importance_level", "type": "TEXT", "not_null": false, "default_value": "'HIGH'", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [], "foreign_keys": [], "primary_keys": ["id"]}, "field_relationships": {"row_count": 7, "column_count": 8, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "table_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "primary_field", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "related_field", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "relationship_type", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "relationship_description", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "usage_example", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [], "foreign_keys": [], "primary_keys": ["id"]}, "query_patterns": {"row_count": 5, "column_count": 9, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "pattern_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "pattern_description", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "natural_language_examples", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "sql_template", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "required_fields", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "business_scenario", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "difficulty_level", "type": "TEXT", "not_null": false, "default_value": "'MEDIUM'", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [], "foreign_keys": [], "primary_keys": ["id"]}, "data_quality_rules": {"row_count": 6, "column_count": 9, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "table_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "field_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "rule_type", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "rule_description", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "validation_sql", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "error_message", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "severity_level", "type": "TEXT", "not_null": false, "default_value": "'WARNING'", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [], "foreign_keys": [], "primary_keys": ["id"]}, "ai_prompt_templates": {"row_count": 6, "column_count": 8, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "template_name", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "template_type", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "template_content", "type": "TEXT", "not_null": true, "default_value": null, "primary_key": false}, {"name": "usage_scenario", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "priority_level", "type": "INTEGER", "not_null": false, "default_value": "5", "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "default_value": "TRUE", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [], "foreign_keys": [], "primary_keys": ["id"]}}, "file_size": 386068480}, "duplicate_tables": ["column_descriptions", "table_descriptions", "business_rules"], "structure_differences": {"column_descriptions": {"column_differences": [{"type": "missing_in_resource", "column": "field_category", "business_db_definition": {"name": "field_category", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "related_fields", "business_db_definition": {"name": "related_fields", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "usage_scenarios", "business_db_definition": {"name": "usage_scenarios", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "calculation_rules", "business_db_definition": {"name": "calculation_rules", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "ai_prompt_hints", "business_db_definition": {"name": "ai_prompt_hints", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "common_values", "business_db_definition": {"name": "common_values", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}]}, "business_rules": {"row_count_difference": {"resource_db": 5, "business_db": 17}}}, "recommendations": [{"type": "DUPLICATE_TABLES", "priority": "HIGH", "description": "发现 3 个重复表名", "action": "建议重新设计数据库架构，确保元数据库和业务数据库的表名不重复", "tables": ["column_descriptions", "table_descriptions", "business_rules"]}, {"type": "STRUCTURE_DIFFERENCE", "priority": "MEDIUM", "description": "表 column_descriptions 在两个数据库中结构不一致", "action": "统一表 column_descriptions 的结构定义", "differences": {"column_differences": [{"type": "missing_in_resource", "column": "field_category", "business_db_definition": {"name": "field_category", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "related_fields", "business_db_definition": {"name": "related_fields", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "usage_scenarios", "business_db_definition": {"name": "usage_scenarios", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "calculation_rules", "business_db_definition": {"name": "calculation_rules", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "ai_prompt_hints", "business_db_definition": {"name": "ai_prompt_hints", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}, {"type": "missing_in_resource", "column": "common_values", "business_db_definition": {"name": "common_values", "type": "TEXT", "not_null": false, "default_value": "''", "primary_key": false}}]}}, {"type": "STRUCTURE_DIFFERENCE", "priority": "MEDIUM", "description": "表 business_rules 在两个数据库中结构不一致", "action": "统一表 business_rules 的结构定义", "differences": {"row_count_difference": {"resource_db": 5, "business_db": 17}}}], "resource_table_names": ["chathistorysnapshot", "schemarelationship", "table_descriptions", "dbconnection", "schematable", "schemacolumn", "chatmessage", "column_descriptions", "valuemapping", "chatsession", "business_rules"], "business_table_names": ["data_quality_rules", "table_descriptions", "query_patterns", "column_descriptions", "ai_prompt_templates", "field_relationships", "financial_data", "business_rules"]}