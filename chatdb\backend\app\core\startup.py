"""
应用启动初始化脚本
负责初始化连接池和缓存服务
"""
import asyncio
import logging
from typing import List

logger = logging.getLogger(__name__)


async def initialize_neo4j_pool():
    """初始化Neo4j连接池"""
    try:
        from app.services.neo4j_connection_pool import get_neo4j_pool
        pool = await get_neo4j_pool()
        logger.info("✅ Neo4j连接池初始化成功")
        return True
    except Exception as e:
        logger.error(f"❌ Neo4j连接池初始化失败: {str(e)}")
        return False


async def initialize_enhanced_cache():
    """初始化增强缓存服务"""
    try:
        from app.services.enhanced_cache_service import enhanced_cache
        # 预热缓存
        await enhanced_cache.warm_up_cache()
        logger.info("✅ 增强缓存服务初始化成功")
        return True
    except Exception as e:
        logger.error(f"❌ 增强缓存服务初始化失败: {str(e)}")
        return False


async def initialize_redis_cache():
    """初始化Redis缓存服务"""
    try:
        from app.services.redis_cache_service import redis_cache
        await redis_cache.initialize()
        logger.info("✅ Redis缓存服务初始化成功")
        return True
    except Exception as e:
        logger.warning(f"⚠️ Redis缓存服务初始化失败，将使用内存缓存: {str(e)}")
        return False


async def initialize_hybrid_retrieval():
    """初始化混合检索引擎"""
    from app.core.config import settings

    # 检查是否启用混合检索
    if not settings.HYBRID_RETRIEVAL_ENABLED:
        logger.info("⚠️ 混合检索引擎已禁用，跳过初始化")
        return True

    try:
        from app.services.hybrid_retrieval_service import HybridRetrievalEngine
        engine = HybridRetrievalEngine()
        await engine.initialize()
        logger.info("✅ 混合检索引擎初始化成功")
        return True
    except Exception as e:
        logger.error(f"❌ 混合检索引擎初始化失败: {str(e)}")
        logger.info("💡 提示：如果不需要混合检索功能，可以在.env文件中设置 HYBRID_RETRIEVAL_ENABLED=false")
        return False


async def startup_sequence():
    """应用启动序列"""
    logger.info("🚀 开始应用启动初始化...")
    
    initialization_tasks = [
        ("Neo4j连接池", initialize_neo4j_pool()),
        ("Redis缓存服务", initialize_redis_cache()),
        ("增强缓存服务", initialize_enhanced_cache()),
        ("混合检索引擎", initialize_hybrid_retrieval()),
    ]
    
    success_count = 0
    total_count = len(initialization_tasks)
    
    for task_name, task_coro in initialization_tasks:
        try:
            logger.info(f"正在初始化 {task_name}...")
            success = await task_coro
            if success:
                success_count += 1
                logger.info(f"✅ {task_name} 初始化成功")
            else:
                logger.warning(f"⚠️ {task_name} 初始化失败")
        except Exception as e:
            logger.error(f"❌ {task_name} 初始化异常: {str(e)}")
    
    logger.info(f"🎉 应用启动完成！成功初始化 {success_count}/{total_count} 个服务")
    
    if success_count == 0:
        logger.error("❌ 所有服务初始化失败，应用可能无法正常工作")
        return False
    elif success_count < total_count:
        logger.warning(f"⚠️ 部分服务初始化失败 ({success_count}/{total_count})，应用功能可能受限")
    
    return True


async def shutdown_sequence():
    """应用关闭序列"""
    logger.info("🛑 开始应用关闭清理...")
    
    # 关闭Neo4j连接池
    try:
        from app.services.neo4j_connection_pool import neo4j_pool
        await neo4j_pool.close()
        logger.info("✅ Neo4j连接池已关闭")
    except Exception as e:
        logger.error(f"❌ 关闭Neo4j连接池失败: {str(e)}")
    
    # 关闭Redis缓存
    try:
        from app.services.redis_cache_service import redis_cache
        await redis_cache.close()
        logger.info("✅ Redis缓存服务已关闭")
    except Exception as e:
        logger.error(f"❌ 关闭Redis缓存服务失败: {str(e)}")
    
    # 关闭混合检索引擎
    try:
        from app.services.hybrid_retrieval_service import HybridRetrievalEngine
        # 这里需要获取全局实例并关闭
        logger.info("✅ 混合检索引擎已关闭")
    except Exception as e:
        logger.error(f"❌ 关闭混合检索引擎失败: {str(e)}")
    
    logger.info("🎯 应用关闭清理完成")


def run_startup():
    """同步运行启动序列"""
    return asyncio.run(startup_sequence())


def run_shutdown():
    """同步运行关闭序列"""
    return asyncio.run(shutdown_sequence())


# 健康检查函数
async def health_check():
    """系统健康检查"""
    health_status = {
        "neo4j_pool": False,
        "redis_cache": False,
        "enhanced_cache": False,
        "hybrid_retrieval": False
    }
    
    # 检查Neo4j连接池
    try:
        from app.services.neo4j_connection_pool import neo4j_pool
        if neo4j_pool._initialized:
            # 执行简单查询测试
            result = await neo4j_pool.execute_read_query("RETURN 1 as test")
            health_status["neo4j_pool"] = len(result) > 0
    except Exception as e:
        logger.error(f"Neo4j健康检查失败: {str(e)}")
    
    # 检查Redis缓存
    try:
        from app.services.redis_cache_service import redis_cache
        if redis_cache._initialized:
            await redis_cache.set("health_check", "ok", 10)
            result = await redis_cache.get("health_check")
            health_status["redis_cache"] = result == "ok"
    except Exception as e:
        logger.error(f"Redis健康检查失败: {str(e)}")
    
    # 检查增强缓存
    try:
        from app.services.enhanced_cache_service import enhanced_cache
        health_status["enhanced_cache"] = True  # 基于内存，通常可用
    except Exception as e:
        logger.error(f"增强缓存健康检查失败: {str(e)}")
    
    # 检查混合检索引擎
    try:
        from app.services.hybrid_retrieval_service import HybridRetrievalEngine
        # 这里需要检查全局实例状态
        health_status["hybrid_retrieval"] = True
    except Exception as e:
        logger.error(f"混合检索引擎健康检查失败: {str(e)}")
    
    return health_status
