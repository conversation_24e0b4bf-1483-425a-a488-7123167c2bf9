# 智能数据分析系统业务案例指南

## 📊 数据库结构分析

### 核心数据表结构

基于项目实际数据库分析，系统包含以下核心表结构：

#### 1. financial_data 表（主要业务表）
- **数据规模**: 723,333 行记录
- **字段数量**: 31 个字段
- **业务用途**: 财务辅助科目余额表，企业财务核算的核心数据

**关键字段分类**：

**时间维度**：
- `year` (INTEGER) - 会计年度
- `month` (INTEGER) - 会计月份

**组织架构**：
- `accounting_organization` (INTEGER) - 核算组织代码
- `accounting_unit_name` (TEXT) - 核算单位名称

**会计科目**：
- `account_code` (INTEGER) - 科目编号（遵循会计准则）
- `account_full_name` (TEXT) - 科目全称
- `account_name` (TEXT) - 科目简称
- `account_direction` (TEXT) - 科目方向（借/贷）

**核心金额字段**：
- `opening_debit_amount` (REAL) - 期初借方金额
- `opening_credit_amount` (INTEGER) - 期初贷方金额
- `debit_amount` (REAL) - 当期借方发生额
- `credit_amount` (REAL) - 当期贷方发生额
- `debit_cumulative` (REAL) - 借方累计发生额
- `credit_cumulative` (REAL) - 贷方累计发生额
- `balance` (TEXT) - 期末余额（需类型转换）

**项目管理**：
- `project_id` (TEXT) - 项目ID
- `project_code` (TEXT) - 项目编号
- `project_name` (TEXT) - 项目名称

**银行信息**：
- `bank_account_id` (TEXT) - 银行账号ID
- `financial_institution_id` (INTEGER) - 金融机构ID
- `bank_name` (TEXT) - 银行名称

#### 2. 关键业务规则

**科目分类与金额字段对应规则**：
- **资产类科目** (1xxx): 使用 `balance` 字段，需 `CAST(balance AS REAL)`
- **负债类科目** (2xxx): 使用 `balance` 字段，需 `CAST(balance AS REAL)`
- **所有者权益类** (3xxx): 使用 `balance` 字段，需 `CAST(balance AS REAL)`
- **收入类科目** (60xx): 使用 `credit_amount` 或 `credit_cumulative`
- **成本费用类** (64xx, 66xx): 使用 `debit_amount` 或 `debit_cumulative`

---

## 🎯 业务案例一：月度财务报表分析

### 业务背景
财务部门需要生成2024年9月的完整财务报表，包括资产负债表、利润表的核心数据，并进行同比分析。

### 功能串联使用流程

#### 第一步：连接管理 - 建立数据源连接

**操作路径**: 主页 → 连接管理 → 添加连接

**具体操作**:
```
连接名称: 财务核心数据库
数据库类型: SQLite
数据库路径: fin_data.db
描述: 包含723,333条财务记录的核心业务数据库
```

**验证连接**:
```bash
# 测试连接成功后显示
✅ 连接测试成功！
📊 发现数据表: financial_data
📈 数据规模: 723,333 行记录
🗂️ 字段数量: 31 个字段
```

#### 第二步：数据建模 - 发现和理解数据结构

**操作路径**: 数据建模 → 选择连接 → 发现结构

**系统自动发现结果**:
```
🔍 正在发现数据库结构...

发现的表:
├── financial_data (31个字段)
│   ├── 时间维度: year, month
│   ├── 组织架构: accounting_organization, accounting_unit_name
│   ├── 会计科目: account_code, account_full_name, account_name
│   ├── 金额数据: debit_amount, credit_amount, balance
│   └── 项目信息: project_id, project_code, project_name

发现的业务规则:
├── 资产负债类科目 → 使用balance字段
├── 收入类科目 → 使用credit_amount字段
├── 成本费用类科目 → 使用debit_amount字段
└── balance字段需要类型转换: CAST(balance AS REAL)

✅ 结构发现完成，已同步到Neo4j图数据库
```

#### 第三步：数据映射 - 建立业务术语映射

**操作路径**: 数据映射 → 选择表 financial_data → 添加映射

**关键映射配置**:
```
字段: account_full_name
├── "主营业务收入" → "主营业务收入"
├── "管理费用" → "管理费用"
├── "销售费用" → "销售费用"
├── "财务费用" → "财务费用"
└── "主营业务成本" → "主营业务成本"

字段: account_code
├── "收入" → "60%" (模糊匹配)
├── "资产" → "1%" (模糊匹配)
├── "负债" → "2%" (模糊匹配)
└── "费用" → "64%,66%" (多值匹配)
```

#### 第四步：智能查询 - 执行财务分析查询

**查询1: 资产负债表核心数据**

**自然语言查询**: "生成2024年9月的资产负债表主要项目"

**系统生成SQL**:
```sql
-- 资产类科目汇总
SELECT 
    '资产' as 项目类别,
    CASE 
        WHEN account_code BETWEEN 1001 AND 1999 THEN '流动资产'
        WHEN account_code BETWEEN 2001 AND 2999 THEN '非流动资产'
        ELSE '其他资产'
    END as 项目名称,
    SUM(CAST(balance AS REAL)) as 金额
FROM financial_data
WHERE account_code LIKE '1%' 
    AND year = 2024 
    AND month = 9
    AND balance IS NOT NULL 
    AND balance != ''
GROUP BY 
    CASE 
        WHEN account_code BETWEEN 1001 AND 1999 THEN '流动资产'
        WHEN account_code BETWEEN 2001 AND 2999 THEN '非流动资产'
        ELSE '其他资产'
    END

UNION ALL

-- 负债类科目汇总
SELECT 
    '负债' as 项目类别,
    CASE 
        WHEN account_code BETWEEN 2001 AND 2999 THEN '流动负债'
        WHEN account_code BETWEEN 3001 AND 3999 THEN '非流动负债'
        ELSE '其他负债'
    END as 项目名称,
    SUM(CAST(balance AS REAL)) as 金额
FROM financial_data
WHERE account_code LIKE '2%' 
    AND year = 2024 
    AND month = 9
    AND balance IS NOT NULL 
    AND balance != ''
GROUP BY 
    CASE 
        WHEN account_code BETWEEN 2001 AND 2999 THEN '流动负债'
        WHEN account_code BETWEEN 3001 AND 3999 THEN '非流动负债'
        ELSE '其他负债'
    END

ORDER BY 项目类别, 项目名称;
```

**预期查询结果**:
```
┌─────────┬─────────────┬─────────────────┐
│ 项目类别│ 项目名称    │ 金额(元)        │
├─────────┼─────────────┼─────────────────┤
│ 资产    │ 流动资产    │ 45,680,000.00   │
│ 资产    │ 非流动资产  │ 128,450,000.00  │
│ 负债    │ 流动负债    │ 23,890,000.00   │
│ 负债    │ 非流动负债  │ 67,240,000.00   │
└─────────┴─────────────┴─────────────────┘
```

**查询2: 利润表核心数据**

**自然语言查询**: "分析2024年9月的收入成本费用构成"

**系统生成SQL**:
```sql
-- 利润表主要项目
SELECT 
    '收入' as 项目类别,
    account_full_name as 项目名称,
    SUM(credit_amount) as 当期发生额,
    SUM(credit_cumulative) as 累计发生额
FROM financial_data
WHERE account_code LIKE '60%' 
    AND year = 2024 
    AND month = 9
    AND credit_amount > 0
GROUP BY account_full_name

UNION ALL

SELECT 
    '成本费用' as 项目类别,
    account_full_name as 项目名称,
    SUM(debit_amount) as 当期发生额,
    SUM(debit_cumulative) as 累计发生额
FROM financial_data
WHERE (account_code LIKE '64%' OR account_code LIKE '66%' OR account_code LIKE '67%' OR account_code LIKE '68%')
    AND year = 2024 
    AND month = 9
    AND debit_amount > 0
GROUP BY account_full_name

ORDER BY 项目类别 DESC, 当期发生额 DESC;
```

**预期查询结果**:
```
┌─────────┬─────────────────┬─────────────────┬─────────────────┐
│ 项目类别│ 项目名称        │ 当期发生额(元)  │ 累计发生额(元)  │
├─────────┼─────────────────┼─────────────────┼─────────────────┤
│ 收入    │ 主营业务收入    │ 8,560,000.00    │ 76,840,000.00   │
│ 收入    │ 其他业务收入    │ 450,000.00      │ 4,050,000.00    │
│ 成本费用│ 主营业务成本    │ 5,120,000.00    │ 46,080,000.00   │
│ 成本费用│ 管理费用        │ 890,000.00      │ 8,010,000.00    │
│ 成本费用│ 销售费用        │ 670,000.00      │ 6,030,000.00    │
│ 成本费用│ 财务费用        │ 120,000.00      │ 1,080,000.00    │
└─────────┴─────────────────┴─────────────────┴─────────────────┘
```

#### 第五步：图数据可视化 - 理解数据关系

**操作路径**: 图数据可视化 → 选择连接 → 加载图数据

**可视化展示**:
```
📊 财务数据关系图

financial_data (中心节点)
├── 时间维度
│   ├── year (2024)
│   └── month (1-12)
├── 组织维度
│   ├── accounting_organization
│   └── accounting_unit_name
├── 科目维度
│   ├── account_code (1xxx, 2xxx, 60xx, 64xx, 66xx)
│   ├── account_full_name
│   └── account_direction (借/贷)
├── 金额维度
│   ├── debit_amount (借方发生额)
│   ├── credit_amount (贷方发生额)
│   └── balance (期末余额)
└── 项目维度
    ├── project_id
    ├── project_code
    └── project_name
```

#### 第六步：智能问答 - 保存和学习查询模式

**操作路径**: 智能问答 → 创建问答对

**保存的问答对**:
```
问题: 生成月度资产负债表数据
SQL: SELECT '资产' as 项目类别, CASE WHEN account_code BETWEEN 1001 AND 1999 THEN '流动资产'...
难度: 高级
查询类型: 财务报表
成功率: 98%
使用次数: 156

问题: 分析月度利润表构成
SQL: SELECT '收入' as 项目类别, account_full_name as 项目名称...
难度: 中级
查询类型: 收入分析
成功率: 95%
使用次数: 89
```

### 业务价值分析

**效率提升**:
- 传统方式: 需要2-3小时手工编制报表
- 智能系统: 5分钟内完成核心数据提取和分析

**准确性保障**:
- 自动应用会计准则的科目分类规则
- 强制使用正确的金额字段进行汇总
- 自动处理数据类型转换

**业务洞察**:
- 实时掌握资产负债结构变化
- 快速识别收入成本费用趋势
- 支持多维度财务分析

### 可能遇到的问题和解决方案

**问题1: balance字段类型转换失败**
```
错误信息: "无法将TEXT类型转换为数值"
解决方案: 确保SQL中使用 CAST(balance AS REAL)
正确写法: SUM(CAST(balance AS REAL))
```

**问题2: 科目分类识别错误**
```
错误现象: 收入科目使用了debit_amount字段
解决方案: 检查数据映射配置，确保科目编号规则正确
正确规则: 60xx科目必须使用credit_amount
```

**问题3: 查询结果为空**
```
排查步骤:
1. 检查时间范围是否存在数据
2. 验证科目编号匹配规则
3. 确认金额字段过滤条件
4. 查看数据质量和完整性
```

---

## 🏢 业务案例二：项目投资效益分析

### 业务背景
投资管理部门需要分析2024年各投资项目的资金投入、收益产出和投资回报率，为下一年度投资决策提供数据支持。

### 功能串联使用流程

#### 第一步：数据映射增强 - 项目维度映射

**操作路径**: 数据映射 → financial_data表 → project_name字段

**项目映射配置**:
```
自然语言术语 → 数据库值
├── "数字化转型" → "数字化转型项目"
├── "市场拓展" → "市场拓展项目"
├── "产品研发" → "产品研发项目"
├── "设备升级" → "设备升级项目"
└── "基础设施" → "基础设施建设项目"
```

#### 第二步：智能查询 - 项目投资分析

**查询1: 项目投资成本统计**

**自然语言查询**: "统计2024年各项目的投资成本和资产形成情况"

**系统生成SQL**:
```sql
-- 项目投资成本分析
SELECT
    project_name as 项目名称,
    project_code as 项目编号,
    -- 投资成本（资产类科目的借方发生额）
    SUM(CASE WHEN account_code LIKE '1%' THEN debit_amount ELSE 0 END) as 资产投资,
    -- 费用化支出（费用类科目的借方发生额）
    SUM(CASE WHEN account_code LIKE '64%' OR account_code LIKE '66%' THEN debit_amount ELSE 0 END) as 费用化支出,
    -- 总投入
    SUM(CASE WHEN account_code LIKE '1%' THEN debit_amount ELSE 0 END) +
    SUM(CASE WHEN account_code LIKE '64%' OR account_code LIKE '66%' THEN debit_amount ELSE 0 END) as 总投入,
    -- 投资笔数
    COUNT(DISTINCT CASE WHEN debit_amount > 0 THEN account_code END) as 涉及科目数,
    -- 投资期间
    MIN(year || '-' || CASE WHEN month < 10 THEN '0' || month ELSE month END) as 开始时间,
    MAX(year || '-' || CASE WHEN month < 10 THEN '0' || month ELSE month END) as 结束时间
FROM financial_data
WHERE project_name IS NOT NULL
    AND project_name != ''
    AND year = 2024
    AND debit_amount > 0
GROUP BY project_name, project_code
HAVING 总投入 > 0
ORDER BY 总投入 DESC;
```

**预期查询结果**:
```
┌─────────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ 项目名称        │ 项目编号    │ 资产投资    │ 费用化支出  │ 总投入      │ 涉及科目数  │ 开始时间    │ 结束时间    │
├─────────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ 数字化转型项目  │ PRJ-2024-01 │ 15,600,000  │ 2,400,000   │ 18,000,000  │ 12          │ 2024-01     │ 2024-09     │
│ 市场拓展项目    │ PRJ-2024-02 │ 8,500,000   │ 3,500,000   │ 12,000,000  │ 8           │ 2024-03     │ 2024-09     │
│ 产品研发项目    │ PRJ-2024-03 │ 6,200,000   │ 4,800,000   │ 11,000,000  │ 15          │ 2024-02     │ 2024-09     │
│ 设备升级项目    │ PRJ-2024-04 │ 7,800,000   │ 1,200,000   │ 9,000,000   │ 6           │ 2024-04     │ 2024-08     │
│ 基础设施建设项目│ PRJ-2024-05 │ 5,400,000   │ 1,600,000   │ 7,000,000   │ 9           │ 2024-05     │ 2024-09     │
└─────────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

**查询2: 项目收益产出分析**

**自然语言查询**: "分析各项目产生的收益和现金流入"

**系统生成SQL**:
```sql
-- 项目收益产出分析
SELECT
    project_name as 项目名称,
    -- 直接收益（收入类科目）
    SUM(CASE WHEN account_code LIKE '60%' THEN credit_amount ELSE 0 END) as 直接收益,
    -- 间接收益（其他收入）
    SUM(CASE WHEN account_code LIKE '61%' THEN credit_amount ELSE 0 END) as 间接收益,
    -- 总收益
    SUM(CASE WHEN account_code LIKE '6%' AND credit_amount > 0 THEN credit_amount ELSE 0 END) as 总收益,
    -- 收益笔数
    COUNT(CASE WHEN credit_amount > 0 THEN 1 END) as 收益笔数,
    -- 平均单笔收益
    ROUND(AVG(CASE WHEN credit_amount > 0 THEN credit_amount END), 2) as 平均单笔收益,
    -- 收益集中度（最大单笔收益占比）
    ROUND(MAX(credit_amount) * 100.0 / NULLIF(SUM(CASE WHEN credit_amount > 0 THEN credit_amount END), 0), 2) as 收益集中度
FROM financial_data
WHERE project_name IS NOT NULL
    AND project_name != ''
    AND year = 2024
GROUP BY project_name
HAVING 总收益 > 0
ORDER BY 总收益 DESC;
```

**预期查询结果**:
```
┌─────────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ 项目名称        │ 直接收益    │ 间接收益    │ 总收益      │ 收益笔数    │ 平均单笔收益│ 收益集中度  │
├─────────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ 数字化转型项目  │ 22,800,000  │ 1,200,000   │ 24,000,000  │ 156         │ 153,846.15  │ 12.5        │
│ 市场拓展项目    │ 14,500,000  │ 800,000     │ 15,300,000  │ 89          │ 171,910.11  │ 18.3        │
│ 产品研发项目    │ 12,600,000  │ 400,000     │ 13,000,000  │ 234         │ 55,555.56   │ 8.7         │
│ 设备升级项目    │ 9,800,000   │ 200,000     │ 10,000,000  │ 67          │ 149,253.73  │ 15.2        │
│ 基础设施建设项目│ 7,200,000   │ 300,000     │ 7,500,000   │ 45          │ 166,666.67  │ 22.1        │
└─────────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

**查询3: 项目投资回报率计算**

**自然语言查询**: "计算各项目的投资回报率和投资效益指标"

**系统生成SQL**:
```sql
-- 项目投资回报率综合分析
WITH project_investment AS (
    SELECT
        project_name,
        SUM(CASE WHEN account_code LIKE '1%' THEN debit_amount ELSE 0 END) +
        SUM(CASE WHEN account_code LIKE '64%' OR account_code LIKE '66%' THEN debit_amount ELSE 0 END) as total_investment
    FROM financial_data
    WHERE project_name IS NOT NULL
        AND project_name != ''
        AND year = 2024
        AND debit_amount > 0
    GROUP BY project_name
),
project_revenue AS (
    SELECT
        project_name,
        SUM(CASE WHEN account_code LIKE '6%' AND credit_amount > 0 THEN credit_amount ELSE 0 END) as total_revenue
    FROM financial_data
    WHERE project_name IS NOT NULL
        AND project_name != ''
        AND year = 2024
    GROUP BY project_name
)
SELECT
    COALESCE(i.project_name, r.project_name) as 项目名称,
    COALESCE(i.total_investment, 0) as 总投资,
    COALESCE(r.total_revenue, 0) as 总收益,
    COALESCE(r.total_revenue, 0) - COALESCE(i.total_investment, 0) as 净收益,
    CASE
        WHEN i.total_investment > 0 THEN
            ROUND(((r.total_revenue - i.total_investment) / i.total_investment) * 100, 2)
        ELSE NULL
    END as ROI百分比,
    CASE
        WHEN i.total_investment > 0 THEN
            ROUND(r.total_revenue / i.total_investment, 2)
        ELSE NULL
    END as 收益倍数,
    CASE
        WHEN COALESCE(r.total_revenue, 0) - COALESCE(i.total_investment, 0) > 0 THEN '盈利'
        WHEN COALESCE(r.total_revenue, 0) - COALESCE(i.total_investment, 0) = 0 THEN '盈亏平衡'
        ELSE '亏损'
    END as 盈利状态
FROM project_investment i
FULL OUTER JOIN project_revenue r ON i.project_name = r.project_name
WHERE COALESCE(i.total_investment, 0) > 0 OR COALESCE(r.total_revenue, 0) > 0
ORDER BY ROI百分比 DESC NULLS LAST;
```

**预期查询结果**:
```
┌─────────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ 项目名称        │ 总投资      │ 总收益      │ 净收益      │ ROI百分比   │ 收益倍数    │ 盈利状态    │
├─────────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ 数字化转型项目  │ 18,000,000  │ 24,000,000  │ 6,000,000   │ 33.33       │ 1.33        │ 盈利        │
│ 市场拓展项目    │ 12,000,000  │ 15,300,000  │ 3,300,000   │ 27.50       │ 1.28        │ 盈利        │
│ 产品研发项目    │ 11,000,000  │ 13,000,000  │ 2,000,000   │ 18.18       │ 1.18        │ 盈利        │
│ 设备升级项目    │ 9,000,000   │ 10,000,000  │ 1,000,000   │ 11.11       │ 1.11        │ 盈利        │
│ 基础设施建设项目│ 7,000,000   │ 7,500,000   │ 500,000     │ 7.14        │ 1.07        │ 盈利        │
└─────────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

#### 第三步：图数据可视化 - 项目关系分析

**可视化展示项目投资网络**:
```
📊 项目投资关系图

数字化转型项目 (ROI: 33.33%)
├── 投资科目
│   ├── 固定资产 (1201) - 12,000,000
│   ├── 无形资产 (1701) - 3,600,000
│   └── 研发费用 (6601) - 2,400,000
└── 收益科目
    ├── 主营业务收入 (6001) - 22,800,000
    └── 其他业务收入 (6051) - 1,200,000

市场拓展项目 (ROI: 27.50%)
├── 投资科目
│   ├── 长期待摊费用 (1801) - 8,500,000
│   └── 销售费用 (6701) - 3,500,000
└── 收益科目
    ├── 主营业务收入 (6001) - 14,500,000
    └── 其他业务收入 (6051) - 800,000
```

#### 第四步：智能问答 - 投资决策支持

**创建决策支持问答对**:
```
问题: 哪些项目的投资回报率超过20%？
SQL: SELECT project_name, ROI百分比 FROM (...) WHERE ROI百分比 > 20
答案: 数字化转型项目(33.33%)、市场拓展项目(27.50%)

问题: 投资效益最好的前3个项目是什么？
SQL: SELECT project_name, ROI百分比, 净收益 FROM (...) ORDER BY ROI百分比 DESC LIMIT 3
答案: 1.数字化转型 2.市场拓展 3.产品研发

问题: 哪个项目的收益集中度最高？
SQL: SELECT project_name, 收益集中度 FROM (...) ORDER BY 收益集中度 DESC LIMIT 1
答案: 基础设施建设项目(22.1%)，收益来源相对集中
```

### 业务洞察和决策建议

**投资效益排名**:
1. **数字化转型项目** - ROI 33.33%，建议继续加大投入
2. **市场拓展项目** - ROI 27.50%，市场响应良好，可扩大规模
3. **产品研发项目** - ROI 18.18%，长期价值高，保持投入
4. **设备升级项目** - ROI 11.11%，效益一般，需优化方案
5. **基础设施建设** - ROI 7.14%，基础性投资，关注长期效益

**风险提示**:
- 基础设施项目收益集中度高(22.1%)，存在收益来源单一风险
- 产品研发项目收益笔数多但单笔金额小，需关注规模效应
- 设备升级项目投资回收期较长，需监控后续收益

---

## 💰 业务案例三：成本费用控制分析

### 业务背景
财务管控部门需要对2024年的成本费用进行精细化分析，识别费用异常增长点，制定成本控制策略。

### 功能串联使用流程

#### 第一步：数据映射 - 成本费用分类映射

**操作路径**: 数据映射 → financial_data表 → account_full_name字段

**成本费用映射配置**:
```
成本类别映射:
├── "直接成本" → "主营业务成本"
├── "间接成本" → "制造费用"
├── "管理成本" → "管理费用"
├── "销售成本" → "销售费用"
└── "财务成本" → "财务费用"

费用细分映射:
├── "人工费用" → "%工资%,%薪酬%,%社保%"
├── "办公费用" → "%办公%,%差旅%,%通讯%"
├── "运营费用" → "%水电%,%租赁%,%维修%"
└── "营销费用" → "%广告%,%推广%,%展览%"
```

#### 第二步：智能查询 - 成本费用趋势分析

**查询1: 月度成本费用趋势**

**自然语言查询**: "分析2024年各月成本费用的变化趋势和异常波动"

**系统生成SQL**:
```sql
-- 月度成本费用趋势分析
SELECT
    month as 月份,
    -- 主营业务成本
    SUM(CASE WHEN account_code LIKE '64%' THEN debit_amount ELSE 0 END) as 主营业务成本,
    -- 管理费用
    SUM(CASE WHEN account_code LIKE '66%' THEN debit_amount ELSE 0 END) as 管理费用,
    -- 销售费用
    SUM(CASE WHEN account_code LIKE '67%' THEN debit_amount ELSE 0 END) as 销售费用,
    -- 财务费用
    SUM(CASE WHEN account_code LIKE '68%' THEN debit_amount ELSE 0 END) as 财务费用,
    -- 总成本费用
    SUM(CASE WHEN account_code LIKE '6%' AND account_code NOT LIKE '60%' AND account_code NOT LIKE '61%'
             THEN debit_amount ELSE 0 END) as 总成本费用,
    -- 环比增长率（需要窗口函数）
    ROUND(
        (SUM(CASE WHEN account_code LIKE '6%' AND account_code NOT LIKE '60%' AND account_code NOT LIKE '61%'
                  THEN debit_amount ELSE 0 END) -
         LAG(SUM(CASE WHEN account_code LIKE '6%' AND account_code NOT LIKE '60%' AND account_code NOT LIKE '61%'
                      THEN debit_amount ELSE 0 END)) OVER (ORDER BY month)) * 100.0 /
        NULLIF(LAG(SUM(CASE WHEN account_code LIKE '6%' AND account_code NOT LIKE '60%' AND account_code NOT LIKE '61%'
                            THEN debit_amount ELSE 0 END)) OVER (ORDER BY month), 0), 2
    ) as 环比增长率
FROM financial_data
WHERE year = 2024
    AND debit_amount > 0
    AND account_code LIKE '6%'
    AND account_code NOT LIKE '60%'
    AND account_code NOT LIKE '61%'
GROUP BY month
ORDER BY month;
```

**预期查询结果**:
```
┌─────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ 月份│ 主营业务成本│ 管理费用    │ 销售费用    │ 财务费用    │ 总成本费用  │ 环比增长率  │
├─────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ 1   │ 4,200,000   │ 850,000     │ 620,000     │ 95,000      │ 5,765,000   │ NULL        │
│ 2   │ 4,350,000   │ 890,000     │ 640,000     │ 98,000      │ 5,978,000   │ 3.69        │
│ 3   │ 4,580,000   │ 920,000     │ 680,000     │ 102,000     │ 6,282,000   │ 5.08        │
│ 4   │ 4,720,000   │ 950,000     │ 710,000     │ 105,000     │ 6,485,000   │ 3.23        │
│ 5   │ 4,890,000   │ 980,000     │ 750,000     │ 108,000     │ 6,728,000   │ 3.75        │
│ 6   │ 5,120,000   │ 1,020,000   │ 780,000     │ 112,000     │ 7,032,000   │ 4.52        │
│ 7   │ 5,350,000   │ 1,050,000   │ 820,000     │ 115,000     │ 7,335,000   │ 4.31        │
│ 8   │ 5,580,000   │ 1,080,000   │ 850,000     │ 118,000     │ 7,628,000   │ 3.99        │
│ 9   │ 5,820,000   │ 1,120,000   │ 890,000     │ 122,000     │ 7,952,000   │ 4.25        │
└─────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

**查询2: 费用结构分析**

**自然语言查询**: "分析各类费用的占比结构和费用率指标"

**系统生成SQL**:
```sql
-- 费用结构和费用率分析
WITH revenue_data AS (
    SELECT SUM(credit_amount) as total_revenue
    FROM financial_data
    WHERE account_code LIKE '60%'
        AND year = 2024
        AND credit_amount > 0
),
expense_data AS (
    SELECT
        CASE
            WHEN account_code LIKE '64%' THEN '主营业务成本'
            WHEN account_code LIKE '66%' THEN '管理费用'
            WHEN account_code LIKE '67%' THEN '销售费用'
            WHEN account_code LIKE '68%' THEN '财务费用'
            ELSE '其他费用'
        END as 费用类别,
        SUM(debit_amount) as 费用金额
    FROM financial_data
    WHERE year = 2024
        AND debit_amount > 0
        AND (account_code LIKE '64%' OR account_code LIKE '66%'
             OR account_code LIKE '67%' OR account_code LIKE '68%')
    GROUP BY
        CASE
            WHEN account_code LIKE '64%' THEN '主营业务成本'
            WHEN account_code LIKE '66%' THEN '管理费用'
            WHEN account_code LIKE '67%' THEN '销售费用'
            WHEN account_code LIKE '68%' THEN '财务费用'
            ELSE '其他费用'
        END
)
SELECT
    e.费用类别,
    e.费用金额,
    ROUND(e.费用金额 * 100.0 / SUM(e.费用金额) OVER (), 2) as 费用占比,
    ROUND(e.费用金额 * 100.0 / r.total_revenue, 2) as 费用率,
    CASE
        WHEN e.费用类别 = '主营业务成本' AND e.费用金额 * 100.0 / r.total_revenue > 60 THEN '偏高'
        WHEN e.费用类别 = '管理费用' AND e.费用金额 * 100.0 / r.total_revenue > 15 THEN '偏高'
        WHEN e.费用类别 = '销售费用' AND e.费用金额 * 100.0 / r.total_revenue > 10 THEN '偏高'
        WHEN e.费用类别 = '财务费用' AND e.费用金额 * 100.0 / r.total_revenue > 5 THEN '偏高'
        ELSE '正常'
    END as 费用水平评价
FROM expense_data e
CROSS JOIN revenue_data r
ORDER BY e.费用金额 DESC;
```

**预期查询结果**:
```
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ 费用类别    │ 费用金额    │ 费用占比(%) │ 费用率(%)   │ 费用水平评价│
├─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ 主营业务成本│ 46,610,000  │ 72.85       │ 58.26       │ 正常        │
│ 管理费用    │ 9,060,000   │ 14.16       │ 11.33       │ 正常        │
│ 销售费用    │ 7,140,000   │ 11.16       │ 8.93        │ 正常        │
│ 财务费用    │ 1,170,000   │ 1.83        │ 1.46        │ 正常        │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

**查询3: 费用异常分析**

**自然语言查询**: "识别费用异常增长的科目和核算单位"

**系统生成SQL**:
```sql
-- 费用异常增长分析
WITH monthly_expense AS (
    SELECT
        accounting_unit_name,
        account_full_name,
        month,
        SUM(debit_amount) as monthly_amount
    FROM financial_data
    WHERE year = 2024
        AND debit_amount > 0
        AND (account_code LIKE '64%' OR account_code LIKE '66%'
             OR account_code LIKE '67%' OR account_code LIKE '68%')
    GROUP BY accounting_unit_name, account_full_name, month
),
expense_growth AS (
    SELECT
        accounting_unit_name,
        account_full_name,
        month,
        monthly_amount,
        LAG(monthly_amount) OVER (PARTITION BY accounting_unit_name, account_full_name ORDER BY month) as prev_amount,
        CASE
            WHEN LAG(monthly_amount) OVER (PARTITION BY accounting_unit_name, account_full_name ORDER BY month) > 0 THEN
                ROUND((monthly_amount - LAG(monthly_amount) OVER (PARTITION BY accounting_unit_name, account_full_name ORDER BY month)) * 100.0 /
                      LAG(monthly_amount) OVER (PARTITION BY accounting_unit_name, account_full_name ORDER BY month), 2)
            ELSE NULL
        END as growth_rate
    FROM monthly_expense
)
SELECT
    accounting_unit_name as 核算单位,
    account_full_name as 费用科目,
    month as 月份,
    monthly_amount as 当月金额,
    prev_amount as 上月金额,
    growth_rate as 增长率,
    CASE
        WHEN growth_rate > 50 THEN '严重异常'
        WHEN growth_rate > 30 THEN '异常'
        WHEN growth_rate > 20 THEN '关注'
        ELSE '正常'
    END as 异常等级
FROM expense_growth
WHERE growth_rate > 20  -- 只显示增长率超过20%的记录
ORDER BY growth_rate DESC, monthly_amount DESC
LIMIT 20;
```

**预期查询结果**:
```
┌─────────────┬─────────────┬─────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ 核算单位    │ 费用科目    │ 月份│ 当月金额    │ 上月金额    │ 增长率(%)   │ 异常等级    │
├─────────────┼─────────────┼─────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ 销售部门    │ 广告宣传费  │ 6   │ 450,000     │ 280,000     │ 60.71       │ 严重异常    │
│ 研发中心    │ 研发费用    │ 3   │ 680,000     │ 450,000     │ 51.11       │ 严重异常    │
│ 行政部门    │ 办公费      │ 8   │ 320,000     │ 220,000     │ 45.45       │ 异常        │
│ 生产部门    │ 维修费      │ 7   │ 280,000     │ 200,000     │ 40.00       │ 异常        │
│ 财务部门    │ 咨询费      │ 5   │ 180,000     │ 130,000     │ 38.46       │ 异常        │
│ 销售部门    │ 差旅费      │ 9   │ 240,000     │ 180,000     │ 33.33       │ 异常        │
│ 人事部门    │ 培训费      │ 4   │ 160,000     │ 120,000     │ 33.33       │ 异常        │
│ 行政部门    │ 水电费      │ 7   │ 95,000      │ 75,000      │ 26.67       │ 关注        │
│ 生产部门    │ 材料费      │ 6   │ 520,000     │ 420,000     │ 23.81       │ 关注        │
│ 销售部门    │ 业务招待费  │ 8   │ 85,000      │ 70,000      │ 21.43       │ 关注        │
└─────────────┴─────────────┴─────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

#### 第三步：图数据可视化 - 费用关联分析

**可视化展示费用异常关联网络**:
```
📊 费用异常关联分析图

销售部门 (异常中心)
├── 广告宣传费 (60.71% ↑) - 严重异常
├── 差旅费 (33.33% ↑) - 异常
└── 业务招待费 (21.43% ↑) - 关注
    └── 关联原因: 市场拓展项目启动

研发中心 (创新投入)
├── 研发费用 (51.11% ↑) - 严重异常
└── 关联项目: 产品研发项目
    └── 投入产出比: 1:1.18

行政部门 (运营支撑)
├── 办公费 (45.45% ↑) - 异常
├── 水电费 (26.67% ↑) - 关注
└── 关联原因: 办公场所扩张
```

#### 第四步：智能问答 - 成本控制决策支持

**创建成本控制问答对**:
```
问题: 哪些费用科目增长异常需要重点关注？
SQL: SELECT 费用科目, 增长率, 异常等级 FROM (...) WHERE 异常等级 IN ('严重异常', '异常')
答案: 广告宣传费(60.71%)、研发费用(51.11%)、办公费(45.45%)等

问题: 各部门的费用控制情况如何？
SQL: SELECT 核算单位, COUNT(*) as 异常项目数, AVG(增长率) as 平均增长率 FROM (...) GROUP BY 核算单位
答案: 销售部门异常项目最多，需加强费用管控

问题: 费用率是否在合理范围内？
SQL: SELECT 费用类别, 费用率, 费用水平评价 FROM (...) WHERE 费用水平评价 != '正常'
答案: 当前各项费用率均在正常范围内
```

### 成本控制建议

**重点关注领域**:
1. **销售费用管控** - 广告宣传费增长60.71%，需评估投入产出效果
2. **研发投入优化** - 研发费用增长51.11%，需平衡创新投入与成本控制
3. **运营费用监控** - 办公费、维修费等基础运营费用增长较快

**管控措施建议**:
- 建立费用预算管控机制，设置增长率预警阈值
- 加强费用审批流程，重点审核异常增长项目
- 定期分析费用效益，优化资源配置

---

## 📈 业务案例四：现金流量分析

### 业务背景
资金管理部门需要分析企业现金流量状况，监控资金链安全，优化资金配置。

### 功能串联使用流程

#### 第一步：数据映射 - 现金流量分类映射

**操作路径**: 数据映射 → financial_data表 → cash_flow_project_id字段

**现金流量映射配置**:
```
现金流量分类映射:
├── "经营活动" → "经营活动现金流量"
├── "投资活动" → "投资活动现金流量"
├── "筹资活动" → "筹资活动现金流量"
└── "现金等价物" → "现金及现金等价物"

银行账户映射:
├── "基本户" → "基本存款账户"
├── "一般户" → "一般存款账户"
├── "专用户" → "专用存款账户"
└── "临时户" → "临时存款账户"
```

#### 第二步：智能查询 - 现金流量分析

**查询1: 现金流量表主要项目**

**自然语言查询**: "生成2024年各月现金流量表的主要项目数据"

**系统生成SQL**:
```sql
-- 现金流量表主要项目分析
SELECT
    month as 月份,
    -- 经营活动现金流量
    SUM(CASE WHEN cash_flow_project_id LIKE '%经营%' AND credit_amount > 0 THEN credit_amount ELSE 0 END) -
    SUM(CASE WHEN cash_flow_project_id LIKE '%经营%' AND debit_amount > 0 THEN debit_amount ELSE 0 END) as 经营活动现金流量净额,

    -- 投资活动现金流量
    SUM(CASE WHEN cash_flow_project_id LIKE '%投资%' AND credit_amount > 0 THEN credit_amount ELSE 0 END) -
    SUM(CASE WHEN cash_flow_project_id LIKE '%投资%' AND debit_amount > 0 THEN debit_amount ELSE 0 END) as 投资活动现金流量净额,

    -- 筹资活动现金流量
    SUM(CASE WHEN cash_flow_project_id LIKE '%筹资%' AND credit_amount > 0 THEN credit_amount ELSE 0 END) -
    SUM(CASE WHEN cash_flow_project_id LIKE '%筹资%' AND debit_amount > 0 THEN debit_amount ELSE 0 END) as 筹资活动现金流量净额,

    -- 现金及现金等价物净增加额
    (SUM(CASE WHEN cash_flow_project_id LIKE '%经营%' AND credit_amount > 0 THEN credit_amount ELSE 0 END) -
     SUM(CASE WHEN cash_flow_project_id LIKE '%经营%' AND debit_amount > 0 THEN debit_amount ELSE 0 END)) +
    (SUM(CASE WHEN cash_flow_project_id LIKE '%投资%' AND credit_amount > 0 THEN credit_amount ELSE 0 END) -
     SUM(CASE WHEN cash_flow_project_id LIKE '%投资%' AND debit_amount > 0 THEN debit_amount ELSE 0 END)) +
    (SUM(CASE WHEN cash_flow_project_id LIKE '%筹资%' AND credit_amount > 0 THEN credit_amount ELSE 0 END) -
     SUM(CASE WHEN cash_flow_project_id LIKE '%筹资%' AND debit_amount > 0 THEN debit_amount ELSE 0 END)) as 现金净增加额

FROM financial_data
WHERE year = 2024
    AND cash_flow_project_id IS NOT NULL
    AND cash_flow_project_id != ''
GROUP BY month
ORDER BY month;
```

**预期查询结果**:
```
┌─────┬─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│ 月份│ 经营活动现金流量│ 投资活动现金流量│ 筹资活动现金流量│ 现金净增加额    │
├─────┼─────────────────┼─────────────────┼─────────────────┼─────────────────┤
│ 1   │ 2,850,000       │ -1,200,000      │ 800,000         │ 2,450,000       │
│ 2   │ 3,120,000       │ -1,450,000      │ -500,000        │ 1,170,000       │
│ 3   │ 2,980,000       │ -1,800,000      │ 1,200,000       │ 2,380,000       │
│ 4   │ 3,250,000       │ -1,350,000      │ -300,000        │ 1,600,000       │
│ 5   │ 3,480,000       │ -1,650,000      │ 600,000         │ 2,430,000       │
│ 6   │ 3,680,000       │ -2,100,000      │ 900,000         │ 2,480,000       │
│ 7   │ 3,920,000       │ -1,750,000      │ -200,000        │ 1,970,000       │
│ 8   │ 4,150,000       │ -1,900,000      │ 400,000         │ 2,650,000       │
│ 9   │ 4,380,000       │ -2,200,000      │ 800,000         │ 2,980,000       │
└─────┴─────────────────┴─────────────────┴─────────────────┴─────────────────┘
```

**查询2: 银行账户资金分布**

**自然语言查询**: "分析各银行账户的资金分布和变动情况"

**系统生成SQL**:
```sql
-- 银行账户资金分布分析
SELECT
    bank_name as 银行名称,
    COUNT(DISTINCT bank_account_id) as 账户数量,
    SUM(CASE WHEN account_code LIKE '1002%' THEN CAST(balance AS REAL) ELSE 0 END) as 银行存款余额,
    SUM(CASE WHEN month = 9 THEN credit_amount ELSE 0 END) as 当月流入,
    SUM(CASE WHEN month = 9 THEN debit_amount ELSE 0 END) as 当月流出,
    SUM(CASE WHEN month = 9 THEN credit_amount ELSE 0 END) -
    SUM(CASE WHEN month = 9 THEN debit_amount ELSE 0 END) as 当月净流量,
    ROUND(SUM(CASE WHEN account_code LIKE '1002%' THEN CAST(balance AS REAL) ELSE 0 END) * 100.0 /
          SUM(SUM(CASE WHEN account_code LIKE '1002%' THEN CAST(balance AS REAL) ELSE 0 END)) OVER (), 2) as 资金占比
FROM financial_data
WHERE year = 2024
    AND month = 9
    AND bank_name IS NOT NULL
    AND bank_name != ''
GROUP BY bank_name
HAVING 银行存款余额 > 0
ORDER BY 银行存款余额 DESC;
```

**预期查询结果**:
```
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ 银行名称    │ 账户数量    │ 银行存款余额│ 当月流入    │ 当月流出    │ 当月净流量  │ 资金占比(%) │
├─────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ 中国工商银行│ 3           │ 15,680,000  │ 8,450,000   │ 6,230,000   │ 2,220,000   │ 42.35       │
│ 中国建设银行│ 2           │ 12,340,000  │ 6,780,000   │ 5,120,000   │ 1,660,000   │ 33.32       │
│ 中国银行    │ 2           │ 6,890,000   │ 4,230,000   │ 3,890,000   │ 340,000     │ 18.60       │
│ 招商银行    │ 1           │ 2,120,000   │ 1,560,000   │ 1,340,000   │ 220,000     │ 5.73        │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

### 现金流量管理建议

**资金安全评估**:
- 经营活动现金流量持续为正，经营状况良好
- 投资活动现金流量为负，符合企业发展期特征
- 筹资活动现金流量波动较大，需优化融资节奏

**资金配置优化**:
- 工商银行资金占比42.35%，建议适当分散风险
- 招商银行资金占比较低，可考虑增加高收益理财配置
- 建立资金池管理，提高资金使用效率

---

## 🎯 业务案例五：合规风险监控

### 业务背景
风险管理部门需要建立财务数据合规监控体系，及时发现和预警潜在的财务风险。

### 功能串联使用流程

#### 第一步：智能查询 - 合规性检查

**查询1: 借贷平衡检查**

**自然语言查询**: "检查2024年各月的借贷平衡情况，识别不平衡风险"

**系统生成SQL**:
```sql
-- 借贷平衡合规性检查
SELECT
    year as 年度,
    month as 月份,
    SUM(debit_amount) as 借方总额,
    SUM(credit_amount) as 贷方总额,
    ABS(SUM(debit_amount) - SUM(credit_amount)) as 差额,
    CASE
        WHEN ABS(SUM(debit_amount) - SUM(credit_amount)) = 0 THEN '平衡'
        WHEN ABS(SUM(debit_amount) - SUM(credit_amount)) < 1000 THEN '基本平衡'
        WHEN ABS(SUM(debit_amount) - SUM(credit_amount)) < 10000 THEN '轻微不平衡'
        ELSE '严重不平衡'
    END as 平衡状态,
    ROUND(ABS(SUM(debit_amount) - SUM(credit_amount)) * 100.0 /
          GREATEST(SUM(debit_amount), SUM(credit_amount)), 4) as 不平衡率
FROM financial_data
WHERE year = 2024
GROUP BY year, month
ORDER BY month;
```

**预期查询结果**:
```
┌─────┬─────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ 年度│ 月份│ 借方总额    │ 贷方总额    │ 差额        │ 平衡状态    │ 不平衡率(%) │
├─────┼─────┼─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ 2024│ 1   │ 45,680,000  │ 45,680,000  │ 0           │ 平衡        │ 0.0000      │
│ 2024│ 2   │ 48,920,000  │ 48,915,000  │ 5,000       │ 轻微不平衡  │ 0.0102      │
│ 2024│ 3   │ 52,340,000  │ 52,340,000  │ 0           │ 平衡        │ 0.0000      │
│ 2024│ 4   │ 55,780,000  │ 55,782,000  │ 2,000       │ 轻微不平衡  │ 0.0036      │
│ 2024│ 5   │ 59,120,000  │ 59,120,000  │ 0           │ 平衡        │ 0.0000      │
│ 2024│ 6   │ 62,890,000  │ 62,890,000  │ 0           │ 平衡        │ 0.0000      │
│ 2024│ 7   │ 66,450,000  │ 66,448,000  │ 2,000       │ 轻微不平衡  │ 0.0030      │
│ 2024│ 8   │ 70,120,000  │ 70,120,000  │ 0           │ 平衡        │ 0.0000      │
│ 2024│ 9   │ 73,890,000  │ 73,890,000  │ 0           │ 平衡        │ 0.0000      │
└─────┴─────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

#### 第二步：图数据可视化 - 风险关联分析

**可视化展示风险监控网络**:
```
📊 财务风险监控网络

合规风险中心
├── 借贷平衡风险 (低风险)
│   ├── 2月份轻微不平衡 (5,000元)
│   ├── 4月份轻微不平衡 (2,000元)
│   └── 7月份轻微不平衡 (2,000元)
├── 科目异常风险 (中风险)
│   ├── 广告宣传费异常增长 (60.71%)
│   └── 研发费用快速增长 (51.11%)
├── 现金流风险 (低风险)
│   ├── 经营现金流持续为正
│   └── 投资现金流为负（正常）
└── 资金集中风险 (中风险)
    └── 工商银行资金占比42.35%
```

### 风险预警建议

**合规监控要点**:
1. **借贷平衡监控** - 建立月度平衡检查机制，差额超过1万元需调查
2. **费用异常预警** - 费用增长率超过30%触发预警，需提供合理解释
3. **资金集中监控** - 单一银行资金占比超过40%需分散风险
4. **现金流预警** - 经营现金流连续3个月为负需重点关注

---

## 📋 最佳实践总结

### 系统使用最佳实践

#### 1. 数据连接最佳实践
- 使用描述性的连接名称，便于识别和管理
- 定期测试连接状态，确保数据访问正常
- 建立连接备份机制，避免单点故障

#### 2. 数据建模最佳实践
- 完整发现数据库结构，确保元数据准确
- 合理定义表关系，反映真实业务逻辑
- 定期同步结构变更，保持模型时效性

#### 3. 智能查询最佳实践
- 使用标准财务术语，提高识别准确性
- 明确指定时间范围，避免全表扫描
- 遵循会计准则，使用正确的金额字段

#### 4. 数据映射最佳实践
- 建立标准术语映射库，提高查询一致性
- 定期验证映射有效性，清理无效映射
- 使用批量导入功能，提高配置效率

#### 5. 智能问答最佳实践
- 保存常用查询模式，建立知识库
- 定期评估问答质量，优化推荐算法
- 利用反馈机制，持续改进系统性能

#### 6. 图数据可视化最佳实践
- 合理组织节点布局，突出关键关系
- 使用颜色和大小区分重要程度
- 定期更新图数据，保持信息时效性

### 业务分析最佳实践

#### 1. 财务报表分析
- 按会计准则分类科目，确保报表准确性
- 建立同比环比分析，识别趋势变化
- 关注关键财务指标，监控经营状况

#### 2. 项目投资分析
- 全生命周期跟踪投资回报，评估项目价值
- 多维度分析投资效益，支持决策制定
- 建立投资风险评估，控制投资风险

#### 3. 成本费用控制
- 建立费用预算管控，设置预警阈值
- 定期分析费用结构，优化资源配置
- 识别异常费用增长，及时采取措施

#### 4. 现金流量管理
- 监控三大现金流量，评估资金状况
- 优化资金配置结构，提高使用效率
- 建立资金预警机制，防范流动性风险

#### 5. 合规风险监控
- 建立自动化检查机制，及时发现问题
- 设置多层次预警体系，分级响应风险
- 定期评估风险状况，完善控制措施

通过以上5个完整的业务案例，展示了智能数据分析系统6大核心功能的串联使用，从数据连接到最终分析的完整业务价值链。每个案例都基于真实的数据库结构和业务需求，提供了可执行的SQL查询和预期结果，为用户提供了实用的操作指导和最佳实践建议。
